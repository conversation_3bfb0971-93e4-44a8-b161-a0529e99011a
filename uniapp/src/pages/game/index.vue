<template>
  <view class="game-page-container">
    <view class="game-header">
      <uni-icons type="back" size="24" color="#333" @click="goBackHome"></uni-icons>
      <text class="header-title">选择关卡</text>
    </view>

    <view v-if="selectedLibraryInfo" class="selected-library-info card">
      <text class="library-name">{{ selectedLibraryInfo.name }}</text>
      <text class="library-description">{{ selectedLibraryInfo.description }}</text>
    </view>

    <!-- 关卡筛选和分页控制 -->
    <view class="level-controls card">
      <view class="control-row">
        <view class="search-box">
          <uni-icons type="search" size="20" color="#999"></uni-icons>
          <input
            v-model="searchKeyword"
            placeholder="搜索关卡..."
            class="search-input"
            @input="handleSearch"
          />
        </view>
        <view class="level-info">
          <text class="total-levels">共{{ levels.length }}关</text>
        </view>
      </view>

      <view class="pagination-row">
        <button
          @click="goToPrevPage"
          :disabled="currentPage <= 1"
          class="page-btn"
          :class="{ disabled: currentPage <= 1 }"
        >
          上一页
        </button>
        <text class="page-info">{{ currentPage }}/{{ totalPages }}</text>
        <button
          @click="goToNextPage"
          :disabled="currentPage >= totalPages"
          class="page-btn"
          :class="{ disabled: currentPage >= totalPages }"
        >
          下一页
        </button>
      </view>
    </view>

    <view class="levels-grid">
      <view
        v-for="level in displayedLevels"
        :key="level.id"
        @click="selectLevel(level)"
        class="level-card card"
        :class="{'level-locked': level.locked}"
        hover-class="level-card-hover"
      >
        <text class="level-icon">{{ level.icon }}</text>
        <text class="level-name">{{ level.name }}</text>
        <text class="level-word-count">{{ level.wordsCount }} 个单词</text>
        <view v-if="level.locked" class="locked-overlay">
          <uni-icons type="locked-filled" size="30" color="white"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 游戏区域 -->
    <view v-if="currentLevel && !showLevelSelection" class="game-area card">
      <view class="game-info-bar">
        <text>关卡: {{ currentLevel.name }}</text>
        <text>分数: {{ gameScore }}</text>
        <text>已配对: {{ matchedPairs }}/{{ totalPairs }}</text>
      </view>

      <view class="game-board">
        <view
          v-for="(tile, index) in gameBoard"
          :key="tile.id"
          class="board-tile"
          :class="{
            'selected': tile.selected,
            'matched': tile.matched
          }"
          :style="{ backgroundColor: tile.color }"
          @click="handleTileClick(index)"
        >
          <text class="tile-word">{{ tile.word ? tile.word.english : '' }}</text>
          <text class="tile-chinese">{{ tile.word ? tile.word.chinese : '' }}</text>
        </view>
      </view>

      <view class="game-controls">
        <button @click="resetGame" class="control-button">重置游戏</button>
        <button @click="quitGame" class="control-button">退出关卡</button>
      </view>
    </view>

    <!-- 关卡完成/失败弹窗 (后续实现) -->
    <view v-if="isGameEndModalVisible" class="modal-overlay">
      <view class="modal-content">
        <text class="modal-title">{{ gameResultText }}</text>
        <text>您的得分: {{ gameScore }}</text>
        <button @click="closeGameEndModalAndQuit" class="modal-button">返回关卡选择</button>
        <button v-if="!gameWon" @click="resetGame" class="modal-button">再试一次</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';

const selectedLibraryInfo = ref(null);
const levels = ref([]);
const currentLevel = ref(null); // 当前选择的关卡，用于后续游戏逻辑
const showLevelSelection = ref(true); // 控制显示关卡选择还是游戏界面

// 分页和搜索相关
const searchKeyword = ref('');
const currentPage = ref(1);
const pageSize = 12; // 每页显示12个关卡
const filteredLevels = ref([]);

// 计算属性
const totalPages = computed(() => {
  return Math.ceil(filteredLevels.value.length / pageSize);
});

const displayedLevels = computed(() => {
  const start = (currentPage.value - 1) * pageSize;
  const end = start + pageSize;
  return filteredLevels.value.slice(start, end);
});

const gameBoard = ref([]);
const gameScore = ref(0);
const selectedTiles = ref([]); // 存储用户选择的卡片
const matchedPairs = ref(0); // 已配对数量
const totalPairs = ref(0); // 总配对数量
const isChecking = ref(false); // 是否正在检查匹配

const BOARD_SIZE = 16; // 4x4 网格，总共16张卡片（8对）
const CARD_COLORS = ['#FFB6C1', '#ADD8E6', '#90EE90', '#FFD700', '#FFA07A', '#DDA0DD', '#F0E68C', '#FFA500']; // 8种颜色
let tileIdCounter = 0;

const isGameEndModalVisible = ref(false);
const gameResultText = ref('');
const gameWon = ref(false);

// 动态生成关卡数据，支持最多1000关
const generateLevels = (totalWords) => {
  const maxLevels = Math.min(Math.floor(totalWords / 8), 1000); // 每关8个单词，最多1000关
  const levels = [];

  for (let i = 1; i <= maxLevels; i++) {
    levels.push({
      id: i,
      name: `第${i}关`,
      icon: getLevelIcon(i),
      wordsCount: 8,
      locked: i > 1 // 第一关解锁，其他关卡需要通关前一关
    });
  }

  return levels;
};

// 获取关卡图标
const getLevelIcon = (levelId) => {
  if (levelId <= 10) {
    const icons = ['1️⃣', '2️⃣', '3️⃣', '4️⃣', '5️⃣', '6️⃣', '7️⃣', '8️⃣', '9️⃣', '🔟'];
    return icons[levelId - 1];
  } else if (levelId <= 100) {
    return '🎯';
  } else if (levelId <= 500) {
    return '🏆';
  } else {
    return '👑';
  }
};

const wordsForCurrentLevel = computed(() => {
  if (selectedLibraryInfo.value && selectedLibraryInfo.value.words && currentLevel.value) {
    // 每个关卡使用8个不同的单词，循环使用词库中的单词
    const words = selectedLibraryInfo.value.words;
    const startIndex = ((currentLevel.value.id - 1) * 8) % words.length;
    const selectedWords = [];

    for (let i = 0; i < 8; i++) {
      const wordIndex = (startIndex + i) % words.length;
      selectedWords.push(words[wordIndex]);
    }

    return selectedWords;
  }
  return [];
});

onLoad(() => {
  // 页面加载时，从本地存储获取选择的词库信息
  const libraryData = uni.getStorageSync('selectedLibrary');
  if (libraryData) {
    try {
      selectedLibraryInfo.value = JSON.parse(libraryData);

      // 动态生成关卡数据
      const totalWords = selectedLibraryInfo.value.words.length;
      const generatedLevels = generateLevels(totalWords);

      // 从本地存储获取已解锁的关卡
      const unlockedLevels = getUnlockedLevels(selectedLibraryInfo.value.id);

      // 更新关卡解锁状态
      levels.value = generatedLevels.map(level => ({
        ...level,
        locked: !unlockedLevels.includes(level.id)
      }));

      // 初始化筛选列表
      filteredLevels.value = levels.value;

    } catch (e) {
      console.error("Failed to parse selected library data:", e);
      uni.showToast({ title: '加载词库数据失败', icon: 'none' });
      goBackHome();
    }
  } else {
    uni.showToast({ title: '未选择词库', icon: 'none' });
    goBackHome(); // 如果没有词库信息，则返回首页
  }
});

// 获取已解锁的关卡列表
const getUnlockedLevels = (libraryId) => {
  const key = `unlockedLevels_${libraryId}`;
  const saved = uni.getStorageSync(key);
  return saved ? JSON.parse(saved) : [1]; // 默认第一关解锁
};

// 解锁下一关
const unlockNextLevel = (libraryId, currentLevelId) => {
  const key = `unlockedLevels_${libraryId}`;
  const unlockedLevels = getUnlockedLevels(libraryId);
  const nextLevelId = currentLevelId + 1;

  if (!unlockedLevels.includes(nextLevelId)) {
    unlockedLevels.push(nextLevelId);
    uni.setStorageSync(key, JSON.stringify(unlockedLevels));

    // 更新当前关卡列表的解锁状态
    const nextLevel = levels.value.find(level => level.id === nextLevelId);
    if (nextLevel) {
      nextLevel.locked = false;
    }
  }
};

const initializeGameBoard = () => {
  const words = wordsForCurrentLevel.value;
  if (words.length < 8) {
    uni.showToast({ title: '词汇数量不足', icon: 'none' });
    return;
  }

  // 创建卡片对：每个单词创建两张卡片
  const cards = [];
  for (let i = 0; i < 8; i++) {
    const word = words[i];
    const color = CARD_COLORS[i];

    // 创建两张相同的卡片
    for (let j = 0; j < 2; j++) {
      cards.push({
        id: tileIdCounter++,
        word: word,
        color: color,
        selected: false,
        matched: false,
        pairId: i // 用于标识配对
      });
    }
  }

  // 随机打乱卡片顺序
  for (let i = cards.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [cards[i], cards[j]] = [cards[j], cards[i]];
  }

  gameBoard.value = cards;
  totalPairs.value = 8;
  matchedPairs.value = 0;
};

const handleTileClick = (index) => {
  // 如果正在检查匹配或游戏结束，不允许点击
  if (isChecking.value || isGameEndModalVisible.value) return;

  const tile = gameBoard.value[index];

  // 如果卡片已经匹配，不允许点击
  if (tile.matched) return;

  // 如果已经选择了两张卡片，不允许再选择
  if (selectedTiles.value.length >= 2) return;

  // 如果点击的是已经选中的卡片，取消选择
  if (tile.selected) {
    tile.selected = false;
    selectedTiles.value = selectedTiles.value.filter(item => item.index !== index);
    return;
  }

  // 选择卡片
  tile.selected = true;
  selectedTiles.value.push({ index, tile });

  // 如果选择了两张卡片，检查是否匹配
  if (selectedTiles.value.length === 2) {
    isChecking.value = true;
    setTimeout(() => {
      checkMatch();
    }, 500); // 延迟0.5秒让用户看清楚选择
  }
};

const checkMatch = () => {
  const [tile1, tile2] = selectedTiles.value;

  // 检查两张卡片的单词是否相同（通过pairId判断）
  if (tile1.tile.pairId === tile2.tile.pairId) {
    // 匹配成功
    tile1.tile.matched = true;
    tile2.tile.matched = true;
    tile1.tile.selected = false;
    tile2.tile.selected = false;

    matchedPairs.value++;
    gameScore.value += 20; // 每对匹配20分

    uni.showToast({
      title: '配对成功！+20分',
      icon: 'success',
      duration: 1000
    });

    // 检查是否完成游戏
    if (matchedPairs.value === totalPairs.value) {
      setTimeout(() => {
        endGame(true);
      }, 1000);
    }
  } else {
    // 匹配失败，取消选择
    tile1.tile.selected = false;
    tile2.tile.selected = false;

    uni.showToast({
      title: '配对失败，请重新选择',
      icon: 'none',
      duration: 800
    });
  }

  // 清除选择状态
  selectedTiles.value = [];
  isChecking.value = false;
};

// 记忆配对游戏不需要消消乐的匹配和填充逻辑

const selectLevel = (level) => {
  if (level.locked) {
    uni.showToast({ title: '该关卡尚未解锁', icon: 'none' });
    return;
  }
  console.log('Selected level:', level);
  currentLevel.value = level;
  showLevelSelection.value = false; // 切换到游戏界面
  resetGame(); // 初始化并开始游戏
};

const goBackHome = () => {
  if (!showLevelSelection.value) { // 如果在游戏界面
    quitGame();
  } else {
    uni.navigateBack({ delta: 1 });
  }
};

const resetGame = () => {
  gameScore.value = 0;
  selectedTiles.value = [];
  matchedPairs.value = 0;
  isChecking.value = false;
  isGameEndModalVisible.value = false;
  initializeGameBoard();
};

const quitGame = () => {
  currentLevel.value = null;
  showLevelSelection.value = true; // 返回关卡选择界面
  isGameEndModalVisible.value = false;
};

const endGame = (won) => {
  gameWon.value = won;
  gameResultText.value = won ? '恭喜过关！' : '挑战失败！';
  isGameEndModalVisible.value = true;
  if (won) {
    // 更新总分
    const currentTotalScore = parseInt(uni.getStorageSync('englishGameTotalScore') || '0');
    uni.setStorageSync('englishGameTotalScore', currentTotalScore + gameScore.value);

    // 解锁下一关
    if (selectedLibraryInfo.value && currentLevel.value) {
      unlockNextLevel(selectedLibraryInfo.value.id, currentLevel.value.id);
    }
  }
};

const closeGameEndModalAndQuit = () => {
  quitGame();
};

// 搜索处理
const handleSearch = () => {
  if (searchKeyword.value.trim() === '') {
    filteredLevels.value = levels.value;
  } else {
    const keyword = searchKeyword.value.toLowerCase();
    filteredLevels.value = levels.value.filter(level =>
      level.name.toLowerCase().includes(keyword) ||
      level.id.toString().includes(keyword)
    );
  }
  currentPage.value = 1; // 搜索后回到第一页
};

// 分页处理
const goToPrevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const goToNextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};
</script>

<style scoped>
.game-page-container {
  min-height: 100vh;
  background-color: #f0e6ff; /* 淡紫色背景 */
  padding: 16rpx;
}
.game-header {
  margin-bottom: 24rpx;
  padding-top: 16rpx;
  display: flex;
  align-items: center;
}
.header-title {
  font-size: 40rpx; /* text-2xl */
  font-weight: bold;
  color: #581c87; /* text-purple-800 */
  margin-left: 12rpx;
}

.card { /* Common card style */
  background-color: white;
  border-radius: 24rpx; /* rounded-xl or 2xl */
  padding: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
}

.selected-library-info {
  /* padding already from .card */
}
.library-name {
  display: block;
  font-size: 36rpx; /* text-xl */
  font-weight: 600; /* font-semibold */
  color: #6d28d9; /* text-purple-700 */
  margin-bottom: 8rpx;
}
.library-description {
  display: block;
  font-size: 24rpx; /* text-sm */
  color: #7e22ce; /* text-purple-600 */
}

.levels-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}
.level-card {
  padding: 24rpx; /* p-6 */
  text-align: center;
  position: relative; /* For locked overlay */
  transition: transform 0.2s, box-shadow 0.2s;
}
.level-card-hover:not(.level-locked) {
  box-shadow: 0 8rpx 16rpx rgba(0,0,0,0.15); /* hover:shadow-lg */
  transform: scale(0.95);
}
.level-icon {
  display: block;
  font-size: 56rpx; /* text-4xl */
  margin-bottom: 8rpx;
}
.level-name {
  display: block;
  font-weight: bold;
  font-size: 32rpx; /* text-lg */
  color: #581c87; /* text-purple-800 */
  margin-bottom: 4rpx;
}
.level-word-count {
  display: block;
  font-size: 24rpx; /* text-sm */
  color: #7e22ce; /* text-purple-600 */
}
.level-locked {
  opacity: 0.5;
  cursor: not-allowed; /* Primarily for H5 */
}
.locked-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(107, 114, 128, 0.5); /* bg-gray-500 bg-opacity-50 */
  border-radius: 24rpx; /* Match parent card */
}

/* 关卡控制样式 */
.level-controls {
  margin-bottom: 24rpx;
}

.control-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  flex: 1;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 28rpx;
  margin-left: 8rpx;
}

.level-info {
  white-space: nowrap;
}

.total-levels {
  font-size: 24rpx;
  color: #666;
}

.pagination-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.page-btn {
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  border-radius: 16rpx;
  background-color: #007aff;
  color: white;
  border: none;
}

.page-btn.disabled {
  background-color: #ccc;
  color: #999;
}

.page-info {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}
.game-area {
  /* padding already from .card */
  margin-top: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.game-info-bar {
  display: flex;
  justify-content: space-around;
  width: 100%;
  padding: 10rpx 0;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #333;
}
.game-board {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(4, 1fr);
  width: 600rpx;
  height: 600rpx;
  gap: 10rpx;
  margin: 0 auto 20rpx;
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 16rpx;
}

.board-tile {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 3rpx solid transparent;
}

.board-tile:hover {
  transform: scale(1.05);
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.15);
}

.board-tile.selected {
  transform: scale(1.1);
  border-color: #ff4500;
  box-shadow: 0 8rpx 16rpx rgba(255, 69, 0, 0.3);
}

.board-tile.matched {
  opacity: 0.6;
  transform: scale(0.95);
  border-color: #4caf50;
  box-shadow: 0 4rpx 8rpx rgba(76, 175, 80, 0.3);
}

.tile-word {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  text-align: center;
}

.tile-chinese {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}
.game-controls {
  display: flex;
  justify-content: space-around;
  width: 100%;
  margin-top: 20rpx;
}
.control-button {
  padding: 10rpx 20rpx;
  font-size: 28rpx;
  /* 可以使用 uni-button 并自定义样式 */
}
.game-start-text {
  display: block;
  font-size: 36rpx; /* text-xl */
  font-weight: bold;
}
/* Modal Styles (simplified) */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.modal-content {
  background-color: white;
  padding: 40rpx;
  border-radius: 16rpx;
  text-align: center;
  min-width: 500rpx;
}
.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: block;
}
.modal-button {
  margin-top: 20rpx;
  padding: 10rpx 30rpx;
  /* 更多样式 */
}
</style>