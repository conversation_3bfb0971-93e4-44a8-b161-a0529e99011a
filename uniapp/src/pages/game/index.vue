<template>
  <view class="game-page-container">
    <view class="game-header">
      <uni-icons type="back" size="24" color="#333" @click="goBackHome"></uni-icons>
      <text class="header-title">选择关卡</text>
    </view>

    <view v-if="selectedLibraryInfo" class="selected-library-info card">
      <text class="library-name">{{ selectedLibraryInfo.name }}</text>
      <text class="library-description">{{ selectedLibraryInfo.description }}</text>
    </view>

    <view class="levels-grid">
      <view
        v-for="level in levels"
        :key="level.id"
        @click="selectLevel(level)"
        class="level-card card"
        :class="{'level-locked': level.locked}"
        hover-class="level-card-hover"
      >
        <text class="level-icon">{{ level.icon }}</text>
        <text class="level-name">{{ level.name }}</text>
        <text class="level-word-count">{{ level.wordsCount }} 个单词</text>
        <view v-if="level.locked" class="locked-overlay">
          <uni-icons type="locked-filled" size="30" color="white"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 游戏区域 -->
    <view v-if="currentLevel && !showLevelSelection" class="game-area card">
      <view class="game-info-bar">
        <text>关卡: {{ currentLevel.name }}</text>
        <text>分数: {{ gameScore }}</text>
        <text>剩余步数: {{ stepsRemaining }}</text>
      </view>

      <view class="game-board">
        <view v-for="(row, rowIndex) in gameBoard" :key="rowIndex" class="board-row">
          <view
            v-for="(tile, colIndex) in row"
            :key="tile.id"
            class="board-tile"
            :style="{ backgroundColor: tile.color }"
            :class="{ 'selected': tile.selected }"
            @click="handleTileClick(rowIndex, colIndex)"
          >
            <text>{{ tile.word ? tile.word.english.charAt(0) : '' }}</text>
          </view>
        </view>
      </view>

      <view class="game-controls">
        <button @click="resetGame" class="control-button">重置游戏</button>
        <button @click="quitGame" class="control-button">退出关卡</button>
      </view>
    </view>

    <!-- 关卡完成/失败弹窗 (后续实现) -->
    <view v-if="isGameEndModalVisible" class="modal-overlay">
      <view class="modal-content">
        <text class="modal-title">{{ gameResultText }}</text>
        <text>您的得分: {{ gameScore }}</text>
        <button @click="closeGameEndModalAndQuit" class="modal-button">返回关卡选择</button>
        <button v-if="!gameWon" @click="resetGame" class="modal-button">再试一次</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';

const selectedLibraryInfo = ref(null);
const levels = ref([]);
const currentLevel = ref(null); // 当前选择的关卡，用于后续游戏逻辑
const showLevelSelection = ref(true); // 控制显示关卡选择还是游戏界面

const gameBoard = ref([]);
const gameScore = ref(0);
const stepsRemaining = ref(20); // 示例步数
const selectedTiles = ref([]); // 存储用户选择的方块 [{row, col, tile}]

const BOARD_SIZE = 6; // 棋盘大小 6x6
const TILE_TYPES = ['#FFB6C1', '#ADD8E6', '#90EE90', '#FFD700', '#FFA07A']; // 浅粉, 浅蓝, 浅绿, 金色, 浅鲑色
let tileIdCounter = 0;

const isGameEndModalVisible = ref(false);
const gameResultText = ref('');
const gameWon = ref(false);

// 模拟的关卡数据结构，实际应根据词库动态生成或获取
const mockLevels = [
  { id: 1, name: '第一关', icon: '1️⃣', wordsCount: 5, locked: false },
  { id: 2, name: '第二关', icon: '2️⃣', wordsCount: 5, locked: false }, // 简单起见，先都解锁
  // { id: 3, name: '第三关', icon: '3️⃣', wordsCount: 10, locked: true },
];

const wordsForCurrentLevel = computed(() => {
  if (selectedLibraryInfo.value && selectedLibraryInfo.value.words && currentLevel.value) {
    // 简单示例：每个关卡使用词库中的前N个单词
    // 实际项目中，您需要更复杂的逻辑来分配单词到不同关卡
    const wordsPerLevel = mockLevels.find(l => l.id === currentLevel.value.id)?.wordsCount || 5;
    return selectedLibraryInfo.value.words.slice(0, wordsPerLevel * BOARD_SIZE); // 确保有足够单词
  }
  return [];
});

onLoad(() => {
  // 页面加载时，从本地存储获取选择的词库信息
  const libraryData = uni.getStorageSync('selectedLibrary');
  if (libraryData) {
    try {
      selectedLibraryInfo.value = JSON.parse(libraryData);
      // 基于词库信息生成或加载关卡数据
      // 这里我们简单使用模拟数据，并假设每个词库都有这些关卡
      // 实际项目中，关卡数量和每个关卡的单词数可能来自 selectedLibraryInfo.value.words
      levels.value = mockLevels.map(level => ({
        ...level,
        // 实际项目中，可以根据 selectedLibraryInfo.value.words.length 来划分关卡单词
        // wordsCount: Math.ceil(selectedLibraryInfo.value.words.length / mockLevels.length)
      }));
    } catch (e) {
      console.error("Failed to parse selected library data:", e);
      uni.showToast({ title: '加载词库数据失败', icon: 'none' });
      goBackHome();
    }
  } else {
    uni.showToast({ title: '未选择词库', icon: 'none' });
    goBackHome(); // 如果没有词库信息，则返回首页
  }
});

const initializeGameBoard = () => {
  const board = [];
  let wordIndex = 0;
  for (let i = 0; i < BOARD_SIZE; i++) {
    const row = [];
    for (let j = 0; j < BOARD_SIZE; j++) {
      const randomTypeIndex = Math.floor(Math.random() * TILE_TYPES.length);
      const word = wordsForCurrentLevel.value[wordIndex % wordsForCurrentLevel.value.length] || null;
      row.push({
        id: tileIdCounter++,
        type: randomTypeIndex,
        color: TILE_TYPES[randomTypeIndex],
        word: word, // 简单示例，每个块可以关联一个单词
        selected: false,
        isEmpty: false, // 用于标记是否为空白格
      });
      if (word) wordIndex++;
    }
    board.push(row);
  }
  gameBoard.value = board;
  // 确保初始棋盘没有可消除的组合 (简化版，实际消消乐需要更复杂的检测和重排)
  // checkForMatchesAndClear(false); // 初始时不计分
};

const handleTileClick = (rowIndex, colIndex) => {
  if (stepsRemaining.value <= 0) {
    endGame(false); // 步数用完
    return;
  }

  const tile = gameBoard.value[rowIndex][colIndex];
  if (tile.isEmpty) return;

  tile.selected = !tile.selected;

  if (tile.selected) {
    selectedTiles.value.push({ row: rowIndex, col: colIndex, tile });
  } else {
    selectedTiles.value = selectedTiles.value.filter(
      (t) => !(t.row === rowIndex && t.col === colIndex)
    );
  }

  if (selectedTiles.value.length === 2) {
    // 尝试交换
    swapTilesAndCheck();
    stepsRemaining.value--;
  } else if (selectedTiles.value.length > 2) {
    // 不允许多选，重置选择
    selectedTiles.value.forEach(t => t.tile.selected = false);
    selectedTiles.value = [];
    tile.selected = true; // 重新选择当前点击的
    selectedTiles.value.push({ row: rowIndex, col: colIndex, tile });
  }
};

const swapTilesAndCheck = async () => {
  const [tile1Pos, tile2Pos] = selectedTiles.value;

  // 检查是否相邻
  const isAdjacent = Math.abs(tile1Pos.row - tile2Pos.row) + Math.abs(tile1Pos.col - tile2Pos.col) === 1;

  if (isAdjacent) {
    // 交换棋盘数据
    const temp = gameBoard.value[tile1Pos.row][tile1Pos.col];
    gameBoard.value[tile1Pos.row][tile1Pos.col] = gameBoard.value[tile2Pos.row][tile2Pos.col];
    gameBoard.value[tile2Pos.row][tile2Pos.col] = temp;

    // 检查交换后是否有匹配
    const matchesFound = await checkForMatchesAndClear(true);

    if (!matchesFound) {
      // 如果没有匹配，则换回来
      // 使用 setTimeout 模拟动画效果或延迟
      setTimeout(() => {
        const tempUndo = gameBoard.value[tile1Pos.row][tile1Pos.col];
        gameBoard.value[tile1Pos.row][tile1Pos.col] = gameBoard.value[tile2Pos.row][tile2Pos.col];
        gameBoard.value[tile2Pos.row][tile2Pos.col] = tempUndo;
        uni.showToast({ title: '无效移动', icon: 'none', duration: 800 });
      }, 300);
    } else {
       // 匹配成功后，持续检查和填充，直到没有新的匹配
      let newMatchesFound = true;
      while(newMatchesFound) {
        await new Promise(resolve => setTimeout(resolve, 300)); // 等待动画或处理
        fillEmptyTiles();
        await new Promise(resolve => setTimeout(resolve, 300));
        newMatchesFound = await checkForMatchesAndClear(true);
      }
    }
  } else {
     uni.showToast({ title: '请选择相邻方块', icon: 'none', duration: 800 });
  }

  // 清除选择状态
  selectedTiles.value.forEach(t => t.tile.selected = false);
  selectedTiles.value = [];

  if (stepsRemaining.value <= 0 && gameScore.value < (currentLevel.value.wordsCount * 10)) { // 假设每个单词10分
      endGame(false);
  } else if (gameScore.value >= (currentLevel.value.wordsCount * 10) ) { // 目标分数
      endGame(true);
  }
};

const checkForMatchesAndClear = async (shouldScore = true) => {
  let matchesMade = false;
  const toClear = new Set(); // 使用Set避免重复标记

  // 简化的匹配逻辑：检查水平和垂直三个或以上相同类型的方块
  // 水平检查
  for (let r = 0; r < BOARD_SIZE; r++) {
    for (let c = 0; c < BOARD_SIZE - 2; c++) {
      if (!gameBoard.value[r][c].isEmpty && gameBoard.value[r][c].type === gameBoard.value[r][c+1].type && gameBoard.value[r][c].type === gameBoard.value[r][c+2].type) {
        toClear.add(`${r}-${c}`);
        toClear.add(`${r}-${c+1}`);
        toClear.add(`${r}-${c+2}`);
        // 可以扩展到检查4个、5个等
        matchesMade = true;
      }
    }
  }
  // 垂直检查
  for (let c = 0; c < BOARD_SIZE; c++) {
    for (let r = 0; r < BOARD_SIZE - 2; r++) {
      if (!gameBoard.value[r][c].isEmpty && gameBoard.value[r][c].type === gameBoard.value[r+1][c].type && gameBoard.value[r][c].type === gameBoard.value[r+2][c].type) {
        toClear.add(`${r}-${c}`);
        toClear.add(`${r+1}-${c}`);
        toClear.add(`${r+2}-${c}`);
        matchesMade = true;
      }
    }
  }

  if (toClear.size > 0) {
    if (shouldScore) {
      gameScore.value += toClear.size * 10; // 每个消除的方块10分
    }
    toClear.forEach(key => {
      const [r, c] = key.split('-').map(Number);
      gameBoard.value[r][c].isEmpty = true; // 标记为空，而不是直接改变颜色或类型
      gameBoard.value[r][c].color = '#FFFFFF'; // 清空背景色
      gameBoard.value[r][c].word = null;
    });
  }
  return matchesMade;
};

const fillEmptyTiles = () => {
  // 从下往上，从右往左遍历，使方块下落
  for (let c = 0; c < BOARD_SIZE; c++) {
    let emptySlots = 0;
    for (let r = BOARD_SIZE - 1; r >= 0; r--) {
      if (gameBoard.value[r][c].isEmpty) {
        emptySlots++;
      } else if (emptySlots > 0) {
        // 将当前方块移动到最下方的空格
        gameBoard.value[r + emptySlots][c] = { ...gameBoard.value[r][c] };
        gameBoard.value[r][c] = createEmptyTile(); // 原位置变空
      }
    }
    // 填充顶部的空位
    for (let r = 0; r < emptySlots; r++) {
      const randomTypeIndex = Math.floor(Math.random() * TILE_TYPES.length);
      const word = wordsForCurrentLevel.value[Math.floor(Math.random() * wordsForCurrentLevel.value.length)] || null;
      gameBoard.value[r][c] = {
        id: tileIdCounter++,
        type: randomTypeIndex,
        color: TILE_TYPES[randomTypeIndex],
        word: word,
        selected: false,
        isEmpty: false,
      };
    }
  }
};

const createEmptyTile = () => ({
  id: tileIdCounter++,
  type: -1, // 特殊类型标记为空
  color: '#FFFFFF', // 透明或背景色
  word: null,
  selected: false,
  isEmpty: true,
});

const selectLevel = (level) => {
  if (level.locked) {
    uni.showToast({ title: '该关卡尚未解锁', icon: 'none' });
    return;
  }
  console.log('Selected level:', level);
  currentLevel.value = level;
  showLevelSelection.value = false; // 切换到游戏界面
  resetGame(); // 初始化并开始游戏
};

const goBackHome = () => {
  if (!showLevelSelection.value) { // 如果在游戏界面
    quitGame();
  } else {
    uni.navigateBack({ delta: 1 });
  }
};

const resetGame = () => {
  gameScore.value = 0;
  stepsRemaining.value = 20; // 或根据关卡设置
  selectedTiles.value = [];
  isGameEndModalVisible.value = false;
  initializeGameBoard();
};

const quitGame = () => {
  currentLevel.value = null;
  showLevelSelection.value = true; // 返回关卡选择界面
  isGameEndModalVisible.value = false;
};

const endGame = (won) => {
  gameWon.value = won;
  gameResultText.value = won ? '恭喜过关！' : '挑战失败！';
  isGameEndModalVisible.value = true;
  if (won) {
    // 更新总分等逻辑
    const currentTotalScore = parseInt(uni.getStorageSync('englishGameTotalScore') || '0');
    uni.setStorageSync('englishGameTotalScore', currentTotalScore + gameScore.value);
  }
};

const closeGameEndModalAndQuit = () => {
  quitGame();
}
</script>

<style scoped>
.game-page-container {
  min-height: 100vh;
  background-color: #f0e6ff; /* 淡紫色背景 */
  padding: 16rpx;
}
.game-header {
  margin-bottom: 24rpx;
  padding-top: 16rpx;
  display: flex;
  align-items: center;
}
.header-title {
  font-size: 40rpx; /* text-2xl */
  font-weight: bold;
  color: #581c87; /* text-purple-800 */
  margin-left: 12rpx;
}

.card { /* Common card style */
  background-color: white;
  border-radius: 24rpx; /* rounded-xl or 2xl */
  padding: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
}

.selected-library-info {
  /* padding already from .card */
}
.library-name {
  display: block;
  font-size: 36rpx; /* text-xl */
  font-weight: 600; /* font-semibold */
  color: #6d28d9; /* text-purple-700 */
  margin-bottom: 8rpx;
}
.library-description {
  display: block;
  font-size: 24rpx; /* text-sm */
  color: #7e22ce; /* text-purple-600 */
}

.levels-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}
.level-card {
  padding: 24rpx; /* p-6 */
  text-align: center;
  position: relative; /* For locked overlay */
  transition: transform 0.2s, box-shadow 0.2s;
}
.level-card-hover:not(.level-locked) {
  box-shadow: 0 8rpx 16rpx rgba(0,0,0,0.15); /* hover:shadow-lg */
  transform: scale(0.95);
}
.level-icon {
  display: block;
  font-size: 56rpx; /* text-4xl */
  margin-bottom: 8rpx;
}
.level-name {
  display: block;
  font-weight: bold;
  font-size: 32rpx; /* text-lg */
  color: #581c87; /* text-purple-800 */
  margin-bottom: 4rpx;
}
.level-word-count {
  display: block;
  font-size: 24rpx; /* text-sm */
  color: #7e22ce; /* text-purple-600 */
}
.level-locked {
  opacity: 0.5;
  cursor: not-allowed; /* Primarily for H5 */
}
.locked-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(107, 114, 128, 0.5); /* bg-gray-500 bg-opacity-50 */
  border-radius: 24rpx; /* Match parent card */
}
.game-area {
  /* padding already from .card */
  margin-top: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.game-info-bar {
  display: flex;
  justify-content: space-around;
  width: 100%;
  padding: 10rpx 0;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #333;
}
.game-board {
  display: grid;
  grid-template-columns: repeat(var(--board-size, 6), 1fr);
  grid-template-rows: repeat(var(--board-size, 6), 1fr);
  width: calc(var(--board-size, 6) * 90rpx); /* 每个格子大约90rpx */
  height: calc(var(--board-size, 6) * 90rpx);
  gap: 5rpx;
  border: 2rpx solid #ccc;
  background-color: #e0e0e0;
  margin-bottom: 20rpx;
}
.board-row {
  display: contents; /* 使子元素直接成为grid的子项 */
}
.board-tile {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  font-weight: bold;
  border: 1rpx solid #eee;
  box-sizing: border-box;
  transition: transform 0.2s, background-color 0.3s;
}
.board-tile.selected {
  transform: scale(0.9);
  border: 4rpx solid #ff4500; /* 橙红色边框表示选中 */
}
.game-controls {
  display: flex;
  justify-content: space-around;
  width: 100%;
  margin-top: 20rpx;
}
.control-button {
  padding: 10rpx 20rpx;
  font-size: 28rpx;
  /* 可以使用 uni-button 并自定义样式 */
}
.game-start-text {
  display: block;
  font-size: 36rpx; /* text-xl */
  font-weight: bold;
}
/* Modal Styles (simplified) */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.modal-content {
  background-color: white;
  padding: 40rpx;
  border-radius: 16rpx;
  text-align: center;
  min-width: 500rpx;
}
.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: block;
}
.modal-button {
  margin-top: 20rpx;
  padding: 10rpx 30rpx;
  /* 更多样式 */
}
</style>