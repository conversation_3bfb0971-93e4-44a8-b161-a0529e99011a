<template>
  <view class="home-container">
    <!-- 顶部用户信息卡片 -->
    <view class="user-info-card card">
      <view class="user-avatar">
        <text class="avatar-emoji">🎓</text>
      </view>
      <view class="user-details">
        <text class="user-name">小学霸</text>
        <text class="user-level">学习等级: {{ userLevel }}</text>
      </view>
      <view class="score-info">
        <view class="score-item">
          <text class="score-label">总积分</text>
          <text class="score-value">{{ totalScore }}</text>
        </view>
        <view class="score-item">
          <text class="score-label">学习进度</text>
          <text class="score-value">{{ studyProgress }}%</text>
        </view>
      </view>
    </view>

    <!-- 标题 -->
    <view class="section-title">
      <text class="title-text">📚 选择词库</text>
      <text class="title-subtitle">选择你想要挑战的词库开始学习吧！</text>
    </view>

    <!-- 词库列表 -->
    <view class="libraries-list">
      <view
        v-for="library in libraries"
        :key="library.id"
        @click="selectLibrary(library)"
        class="library-card card"
        hover-class="library-card-hover"
      >
        <view class="library-header">
          <text class="library-icon">{{ library.icon }}</text>
          <view class="library-info">
            <text class="library-name">{{ library.name }}</text>
            <text class="library-level">{{ library.difficulty }}</text>
          </view>
          <view class="library-stats">
            <text class="word-count">{{ library.words.length }} 词</text>
          </view>
        </view>

        <text class="library-description">{{ library.description }}</text>

        <!-- 词汇预览 -->
        <view class="word-preview">
          <text class="preview-label">词汇预览:</text>
          <view class="preview-words">
            <text
              v-for="(word, index) in library.words.slice(0, 3)"
              :key="index"
              class="preview-word"
            >
              {{ word.english }}
            </text>
            <text v-if="library.words.length > 3" class="more-words">...</text>
          </view>
        </view>

        <!-- 进度条 -->
        <view class="progress-section">
          <text class="progress-label">学习进度</text>
          <view class="progress-bar">
            <view
              class="progress-fill"
              :style="{ width: library.progress + '%' }"
            ></view>
          </view>
          <text class="progress-text">{{ library.progress }}%</text>
        </view>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration">
      <text class="decoration-text">🌟 加油学习，成为英语小达人！ 🌟</text>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 用户数据
const totalScore = ref(0);
const userLevel = ref(1);
const studyProgress = computed(() => {
  // 基于总积分计算学习进度
  return Math.min(Math.floor(totalScore.value / 10), 100);
});

// 模拟词库数据
const libraries = ref([
  {
    id: 1,
    name: '小学基础词汇',
    icon: '🎒',
    difficulty: '初级',
    description: '适合小学生的基础英语单词，包含日常生活中常用的词汇。',
    progress: 45,
    words: [
      { english: 'apple', chinese: '苹果', phonetic: '/ˈæpl/' },
      { english: 'book', chinese: '书', phonetic: '/bʊk/' },
      { english: 'cat', chinese: '猫', phonetic: '/kæt/' },
      { english: 'dog', chinese: '狗', phonetic: '/dɔːɡ/' },
      { english: 'egg', chinese: '鸡蛋', phonetic: '/eɡ/' },
      { english: 'fish', chinese: '鱼', phonetic: '/fɪʃ/' },
      { english: 'green', chinese: '绿色', phonetic: '/ɡriːn/' },
      { english: 'house', chinese: '房子', phonetic: '/haʊs/' },
      { english: 'ice', chinese: '冰', phonetic: '/aɪs/' },
      { english: 'jump', chinese: '跳', phonetic: '/dʒʌmp/' }
    ]
  },
  {
    id: 2,
    name: '动物世界',
    icon: '🦁',
    difficulty: '初级',
    description: '学习各种可爱的动物英语单词，让孩子爱上大自然。',
    progress: 20,
    words: [
      { english: 'lion', chinese: '狮子', phonetic: '/ˈlaɪən/' },
      { english: 'tiger', chinese: '老虎', phonetic: '/ˈtaɪɡər/' },
      { english: 'elephant', chinese: '大象', phonetic: '/ˈelɪfənt/' },
      { english: 'monkey', chinese: '猴子', phonetic: '/ˈmʌŋki/' },
      { english: 'rabbit', chinese: '兔子', phonetic: '/ˈræbɪt/' },
      { english: 'bird', chinese: '鸟', phonetic: '/bɜːrd/' },
      { english: 'horse', chinese: '马', phonetic: '/hɔːrs/' },
      { english: 'cow', chinese: '牛', phonetic: '/kaʊ/' }
    ]
  },
  {
    id: 3,
    name: '颜色彩虹',
    icon: '🌈',
    difficulty: '入门',
    description: '认识各种美丽的颜色，让世界变得更加绚烂多彩。',
    progress: 80,
    words: [
      { english: 'red', chinese: '红色', phonetic: '/red/' },
      { english: 'blue', chinese: '蓝色', phonetic: '/bluː/' },
      { english: 'yellow', chinese: '黄色', phonetic: '/ˈjeloʊ/' },
      { english: 'green', chinese: '绿色', phonetic: '/ɡriːn/' },
      { english: 'purple', chinese: '紫色', phonetic: '/ˈpɜːrpl/' },
      { english: 'orange', chinese: '橙色', phonetic: '/ˈɔːrɪndʒ/' },
      { english: 'pink', chinese: '粉色', phonetic: '/pɪŋk/' },
      { english: 'black', chinese: '黑色', phonetic: '/blæk/' },
      { english: 'white', chinese: '白色', phonetic: '/waɪt/' }
    ]
  },
  {
    id: 4,
    name: '数字王国',
    icon: '🔢',
    difficulty: '入门',
    description: '学习基础数字英语，为数学学习打下良好基础。',
    progress: 60,
    words: [
      { english: 'one', chinese: '一', phonetic: '/wʌn/' },
      { english: 'two', chinese: '二', phonetic: '/tuː/' },
      { english: 'three', chinese: '三', phonetic: '/θriː/' },
      { english: 'four', chinese: '四', phonetic: '/fɔːr/' },
      { english: 'five', chinese: '五', phonetic: '/faɪv/' },
      { english: 'six', chinese: '六', phonetic: '/sɪks/' },
      { english: 'seven', chinese: '七', phonetic: '/ˈsevn/' },
      { english: 'eight', chinese: '八', phonetic: '/eɪt/' },
      { english: 'nine', chinese: '九', phonetic: '/naɪn/' },
      { english: 'ten', chinese: '十', phonetic: '/ten/' }
    ]
  }
]);

// 选择词库
const selectLibrary = (library) => {
  console.log('Selected library:', library);

  // 将选择的词库信息存储到本地
  uni.setStorageSync('selectedLibrary', JSON.stringify(library));

  // 显示选择提示
  uni.showToast({
    title: `已选择: ${library.name}`,
    icon: 'success',
    duration: 1500
  });

  // 延迟跳转，让用户看到提示
  setTimeout(() => {
    uni.navigateTo({
      url: '/pages/game/index'
    });
  }, 1500);
};

// 页面加载时获取用户数据
onMounted(() => {
  // 从本地存储获取总积分
  const savedScore = uni.getStorageSync('englishGameTotalScore');
  if (savedScore) {
    totalScore.value = parseInt(savedScore);
  }

  // 计算用户等级（每100分升一级）
  userLevel.value = Math.floor(totalScore.value / 100) + 1;
});
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
}

/* 通用卡片样式 */
.card {
  background-color: white;
  border-radius: 24rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* 用户信息卡片 */
.user-info-card {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  color: white;
  margin-bottom: 32rpx;
}

.user-avatar {
  margin-right: 24rpx;
}

.avatar-emoji {
  font-size: 80rpx;
  display: block;
}

.user-details {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  color: #4a5568;
}

.user-level {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
}

.score-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.score-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 12rpx;
}

.score-label {
  font-size: 20rpx;
  color: #6b7280;
  margin-bottom: 4rpx;
}

.score-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #4a5568;
}

/* 标题区域 */
.section-title {
  text-align: center;
  margin-bottom: 32rpx;
}

.title-text {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.title-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

/* 词库列表 */
.libraries-list {
  margin-bottom: 40rpx;
}

.library-card {
  position: relative;
  overflow: hidden;
}

.library-card-hover {
  transform: translateY(-8rpx);
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.15);
}

.library-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.library-icon {
  font-size: 64rpx;
  margin-right: 20rpx;
}

.library-info {
  flex: 1;
}

.library-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 4rpx;
}

.library-level {
  display: block;
  font-size: 24rpx;
  color: #718096;
  background-color: #e2e8f0;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
}

.library-stats {
  text-align: right;
}

.word-count {
  font-size: 28rpx;
  color: #4299e1;
  font-weight: 600;
}

.library-description {
  display: block;
  font-size: 26rpx;
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

/* 词汇预览 */
.word-preview {
  margin-bottom: 20rpx;
}

.preview-label {
  font-size: 24rpx;
  color: #718096;
  margin-bottom: 8rpx;
  display: block;
}

.preview-words {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.preview-word {
  background-color: #ebf8ff;
  color: #2b6cb0;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.more-words {
  color: #718096;
  font-size: 22rpx;
  align-self: center;
}

/* 进度条 */
.progress-section {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.progress-label {
  font-size: 24rpx;
  color: #718096;
  white-space: nowrap;
}

.progress-bar {
  flex: 1;
  height: 12rpx;
  background-color: #e2e8f0;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #48bb78 0%, #38a169 100%);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 22rpx;
  color: #48bb78;
  font-weight: 600;
  white-space: nowrap;
}

/* 底部装饰 */
.bottom-decoration {
  text-align: center;
  padding: 40rpx 0;
}

.decoration-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}
</style>